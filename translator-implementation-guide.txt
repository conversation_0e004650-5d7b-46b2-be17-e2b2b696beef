# HƯỚNG DẪN TRIỂN KHAI CHỨC NĂNG TRANSLATOR

## 1. CẬP NHẬT IDENTITY SERVICE

### Thêm Permission mới
Thêm permission TRANSLATOR_MANAGEMENT vào bảng permissions:
- name: "TRANSLATOR_MANAGEMENT"
- description: "Quyền dịch và quản lý truyện của riêng mình"

### Thêm Role mới
Thêm role TRANSLATOR vào bảng roles:
- name: "TRANSLATOR"
- description: "Dịch giả"
- permissions: [TRANSLATOR_MANAGEMENT]

### Cập nhật ApplicationInitConfig.java
```java
@Bean
@Transactional
ApplicationRunner applicationRunner(UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository) {
    return args -> {
        // Thêm permission mới cho translator
        if (!permissionRepository.existsByName("TRANSLATOR_MANAGEMENT")) {
            permissionRepository.save(Permission.builder().name("TRANSLATOR_MANAGEMENT")
                    .description("Quyền dịch và quản lý truyện của riêng mình")
                    .build());
        }
        
        // Thêm role mới cho translator
        if (!roleRepository.existsByName("TRANSLATOR")) {
            roleRepository.save(Role.builder().name("TRANSLATOR")
                    .permissions(Set.of(permissionRepository.findByName("TRANSLATOR_MANAGEMENT")))
                    .description("Dịch giả")
                    .build());
        }
        
        // Phần còn lại giữ nguyên
    };
}
```

## 2. CẬP NHẬT MANGA SERVICE

### Cập nhật Entity Manga.java
Thêm trường createdBy để lưu ID người tạo:
```java
@Column(name = "created_by", updatable = false)
String createdBy;
```

### Cập nhật Entity Chapter.java
Thêm trường createdBy để lưu ID người tạo:
```java
@Column(name = "created_by", updatable = false)
String createdBy;
```

### Cập nhật MangaRepository.java
Thêm các phương thức tìm kiếm theo người tạo:
```java
Page<Manga> findByCreatedByAndDeletedFalse(String createdBy, Pageable pageable);

@Query("SELECT m FROM Manga m WHERE m.deleted = false AND m.createdBy = :createdBy " +
       "AND (:keyword IS NULL OR LOWER(m.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
       "OR LOWER(m.author) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
       "AND (:genreName IS NULL OR EXISTS (SELECT g FROM m.genres g WHERE LOWER(g.name) = LOWER(:genreName))) " +
       "AND (:status IS NULL OR m.status = :status) " +
       "AND (:yearOfRelease IS NULL OR m.yearOfRelease = :yearOfRelease)")
Page<Manga> searchAndFilterByCreatedBy(
        @Param("keyword") String keyword,
        @Param("genreName") String genreName,
        @Param("status") MangaStatus status,
        @Param("yearOfRelease") Integer yearOfRelease,
        @Param("createdBy") String createdBy,
        Pageable pageable);
```

### Cập nhật ChapterRepository.java
Thêm các phương thức tìm kiếm theo người tạo:
```java
List<Chapter> findByCreatedBy(String createdBy);

@Query("SELECT c FROM Chapter c WHERE c.manga.id = :mangaId AND c.createdBy = :createdBy")
List<Chapter> findByMangaIdAndCreatedBy(@Param("mangaId") String mangaId, @Param("createdBy") String createdBy);
```

### Cập nhật MangaService.java
Thêm phương thức kiểm tra quyền sở hữu và lọc truyện theo người tạo:
```java
// Thêm phương thức kiểm tra quyền sở hữu
public void checkOwnership(String mangaId, String userId) {
    Manga manga = mangaRepository.findById(mangaId)
            .orElseThrow(() -> new AppException(ErrorCode.MANGA_NOT_FOUND));
    
    if (!manga.getCreatedBy().equals(userId)) {
        throw new AppException(ErrorCode.UNAUTHORIZED_OPERATION);
    }
}

// Thêm phương thức lọc truyện theo người tạo
public Page<MangaManagementResponse> searchAndFilterMangasByCreatedBy(
        String keyword, String genreName, String statusStr, Integer yearOfRelease, 
        String createdBy, Pageable pageable) {
    
    MangaStatus status = null;
    if (statusStr != null && !statusStr.isEmpty()) {
        try {
            status = MangaStatus.valueOf(statusStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            // Bỏ qua lỗi, status vẫn là null
        }
    }
    
    Page<Manga> mangasPage = mangaRepository.searchAndFilterByCreatedBy(
            keyword, genreName, status, yearOfRelease, createdBy, pageable);
    
    List<String> mangaIds = mangasPage.getContent().stream()
            .map(Manga::getId).collect(Collectors.toList());
    Map<String, Integer> chapterCountsMap = getChapterCountsMap(mangaIds);
    
    return mangasPage.map(manga -> enrichManagementResponse(manga, chapterCountsMap));
}

// Cập nhật phương thức createManga để lưu người tạo
@Transactional
public MangaResponse createManga(MangaRequest request) {
    // Kiểm tra xem manga đã tồn tại chưa
    Manga existingManga = mangaRepository.findByTitle(request.getTitle());
    if (existingManga != null) {
        throw new AppException(ErrorCode.MANGA_ALREADY_EXISTS);
    }
    
    // Lấy thông tin người dùng hiện tại
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String currentUserId = authentication.getName();
    
    var manga = mangaMapper.toManga(request);
    
    // Thiết lập người tạo
    manga.setCreatedBy(currentUserId);
    
    // Các xử lý khác giữ nguyên
    // ...
    
    return mangaMapper.toMangaResponse(manga);
}

// Cập nhật phương thức updateManga để kiểm tra quyền sở hữu
@Transactional
public MangaResponse updateManga(String id, MangaRequest request) {
    var manga = mangaRepository.findById(id)
            .orElseThrow(() -> new AppException(ErrorCode.MANGA_NOT_FOUND));
    
    // Kiểm tra quyền sở hữu
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String currentUserId = authentication.getName();
    Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
    
    boolean hasTranslatorManagement = authorities.stream()
            .anyMatch(a -> a.getAuthority().equals("TRANSLATOR_MANAGEMENT"));
    boolean hasMangaManagement = authorities.stream()
            .anyMatch(a -> a.getAuthority().equals("MANGA_MANAGEMENT"));
    
    // Nếu chỉ có quyền TRANSLATOR_MANAGEMENT, kiểm tra quyền sở hữu
    if (hasTranslatorManagement && !hasMangaManagement && 
        !manga.getCreatedBy().equals(currentUserId)) {
        throw new AppException(ErrorCode.UNAUTHORIZED_OPERATION);
    }
    
    // Tiếp tục xử lý cập nhật như bình thường
    // ...
}
```

### Cập nhật ChapterService.java
Tương tự, cập nhật ChapterService để lưu người tạo và kiểm tra quyền sở hữu.

### Cập nhật MangaController.java
```java
@GetMapping()
@PreAuthorize("hasAnyAuthority('MANGA_MANAGEMENT', 'SYSTEM_MANAGEMENT', 'TRANSLATOR_MANAGEMENT')")
ApiResponse<Page<MangaManagementResponse>> getAllMangas(
        @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "genreName", required = false) String genreName,
        @RequestParam(value = "status", required = false) String status,
        @RequestParam(value = "yearOfRelease", required = false) Integer yearOfRelease,
        @PageableDefault(size = 10) Pageable pageable,
        @AuthenticationPrincipal Jwt jwt
) {
    // Lấy thông tin người dùng và quyền
    String userId = jwt.getSubject();
    Collection<String> authorities = jwt.getClaimAsStringList("scope");
    
    boolean hasTranslatorManagement = authorities.contains("TRANSLATOR_MANAGEMENT");
    boolean hasMangaManagement = authorities.contains("MANGA_MANAGEMENT");
    boolean hasSystemManagement = authorities.contains("SYSTEM_MANAGEMENT");
    
    // Nếu chỉ có quyền TRANSLATOR_MANAGEMENT, chỉ lấy truyện của họ
    if (hasTranslatorManagement && !hasMangaManagement && !hasSystemManagement) {
        return ApiResponse.<Page<MangaManagementResponse>>builder()
                .message("Filtered mangas retrieved successfully")
                .result(mangaService.searchAndFilterMangasByCreatedBy(keyword, genreName, status, yearOfRelease, userId, pageable))
                .build();
    }
    
    // Nếu có quyền MANGA_MANAGEMENT hoặc SYSTEM_MANAGEMENT, xem tất cả truyện
    if (keyword != null || genreName != null || status != null || yearOfRelease != null) {
        return ApiResponse.<Page<MangaManagementResponse>>builder()
                .message("Filtered mangas retrieved successfully")
                .result(mangaService.searchAndFilterActiveMangas(keyword, genreName, status, yearOfRelease, pageable))
                .build();
    }
    
    return ApiResponse.<Page<MangaManagementResponse>>builder()
            .message("Mangas retrieved successfully")
            .result(mangaService.getAllMangas(pageable))
            .build();
}
```

### Cập nhật SecurityConfig.java
```java
@Bean
public SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception {
    httpSecurity.authorizeHttpRequests(request ->
            request.requestMatchers(HttpMethod.GET, PUBLIC_ENDPOINTS).permitAll()
                    .requestMatchers(HttpMethod.POST, "/mangas/search/advanced").permitAll()
                    .requestMatchers(HttpMethod.POST, "/mangas").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.POST, "/chapters").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.PUT, "/mangas/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.PUT, "/chapters/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.DELETE, "/mangas/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.DELETE, "/chapters/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .anyRequest()
                    .authenticated());

    // Phần còn lại giữ nguyên
}
```

### Thêm ErrorCode mới
```java
public enum ErrorCode {
    // Các mã lỗi hiện có...
    
    UNAUTHORIZED_OPERATION(403, "Bạn không có quyền thực hiện thao tác này")
    
    // Các mã lỗi khác...
}
```

## 3. TẠO TRANSLATOR PORTAL RIÊNG BIỆT

### Cập nhật AuthContext.tsx
```typescript
const [hasTranslatorManagement, setHasTranslatorManagement] = useState<boolean>(() => {
    const token = localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
    if (token) {
        const { permissions } = parseTokenPermissions(token);
        return permissions.includes('TRANSLATOR_MANAGEMENT');
    }
    return false;
});

// Trong hàm parseTokenPermissions
const parseTokenPermissions = (token: string) => {
    try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        const payload = JSON.parse(jsonPayload);
        const permissions: string[] = payload.scope ? payload.scope.split(' ') : [];

        return {
            permissions,
            hasMangaManagement: permissions.includes('MANGA_MANAGEMENT'),
            hasSystemManagement: permissions.includes('SYSTEM_MANAGEMENT'),
            hasTranslatorManagement: permissions.includes('TRANSLATOR_MANAGEMENT')
        };
    } catch (error) {
        console.error("Lỗi khi parse token:", error);
        return {
            permissions: [],
            hasMangaManagement: false,
            hasSystemManagement: false,
            hasTranslatorManagement: false
        };
    }
};

// Thêm logic redirect sau khi login
const redirectAfterLogin = () => {
    const { hasMangaManagement, hasSystemManagement, hasTranslatorManagement } = parseTokenPermissions(
        localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN) || ''
    );

    if (hasSystemManagement || hasMangaManagement) {
        navigate('/admin/dashboard');
    } else if (hasTranslatorManagement) {
        navigate('/translator/my-mangas');
    } else {
        navigate('/login');
    }
};
```

### Tạo TranslatorLayout.tsx
```tsx
import React, { useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faBook,
    faBookOpen,
    faSignOutAlt,
    faUser,
    faBars,
    faTimes
} from '@fortawesome/free-solid-svg-icons';

interface TranslatorLayoutProps {
    children: React.ReactNode;
}

const TranslatorLayout: React.FC<TranslatorLayoutProps> = ({ children }) => {
    const { user, logout } = useAuth();
    const location = useLocation();
    const navigate = useNavigate();
    const [sidebarOpen, setSidebarOpen] = React.useState(false);

    const menuItems = [
        { path: '/translator/my-mangas', icon: faBook, label: 'Truyện của tôi' },
        { path: '/translator/my-chapters', icon: faBookOpen, label: 'Chương của tôi' },
    ];

    const handleLogout = () => {
        logout();
        navigate('/login');
    };

    return (
        <div className="min-h-screen bg-gray-100">
            {/* Header */}
            <header className="bg-white shadow-sm border-b border-gray-200">
                <div className="flex items-center justify-between px-4 py-3">
                    <div className="flex items-center">
                        <button
                            onClick={() => setSidebarOpen(!sidebarOpen)}
                            className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                        >
                            <FontAwesomeIcon icon={sidebarOpen ? faTimes : faBars} />
                        </button>
                        <h1 className="ml-2 text-xl font-semibold text-blue-600">
                            Translator Dashboard
                        </h1>
                    </div>

                    <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                            <FontAwesomeIcon icon={faUser} className="text-gray-500" />
                            <span className="text-sm text-gray-700">{user?.username}</span>
                        </div>
                        <button
                            onClick={handleLogout}
                            className="flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                        >
                            <FontAwesomeIcon icon={faSignOutAlt} />
                            <span>Đăng xuất</span>
                        </button>
                    </div>
                </div>
            </header>

            <div className="flex">
                {/* Sidebar */}
                <aside className={`
                    fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
                    lg:translate-x-0 lg:static lg:inset-0
                    ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
                `}>
                    <div className="flex flex-col h-full pt-16 lg:pt-0">
                        <nav className="flex-1 px-4 py-6 space-y-2">
                            {menuItems.map((item) => (
                                <Link
                                    key={item.path}
                                    to={item.path}
                                    className={`
                                        flex items-center space-x-3 px-4 py-3 text-sm font-medium rounded-lg transition-colors
                                        ${location.pathname === item.path
                                            ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-700'
                                            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                                        }
                                    `}
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <FontAwesomeIcon icon={item.icon} className="w-5 h-5" />
                                    <span>{item.label}</span>
                                </Link>
                            ))}
                        </nav>
                    </div>
                </aside>

                {/* Overlay for mobile */}
                {sidebarOpen && (
                    <div
                        className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
                        onClick={() => setSidebarOpen(false)}
                    />
                )}

                {/* Main content */}
                <main className="flex-1 lg:ml-0">
                    <div className="p-6">
                        {children}
                    </div>
                </main>
            </div>
        </div>
    );
};

export default TranslatorLayout;
```

### Tạo trang TranslatorMyMangas.tsx (trong thư mục translator)
```tsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import mangaService from '../../services/manga-service';
import { MangaResponse } from '../../types/manga';
import { Pagination } from '../../components/common/Pagination';
import { SearchBar } from '../../components/common/SearchBar';
import { Button } from '../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faEdit, faTrash, faEye } from '@fortawesome/free-solid-svg-icons';

const TranslatorMyMangas = () => {
  const navigate = useNavigate();
  const [mangas, setMangas] = useState<MangaResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch mangas created by current user
  const fetchMyMangas = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const response = await mangaService.getMyMangas(page - 1, 10, search);
      if (response) {
        setMangas(response.content);
        setTotalPages(response.totalPages);
        setCurrentPage(response.number + 1);
      }
    } catch (error) {
      console.error('Error fetching my mangas:', error);
      toast.error('Không thể tải danh sách truyện của bạn', { position: 'top-right' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMyMangas(currentPage, searchTerm);
  }, [currentPage]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchMyMangas(1, term);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateManga = () => {
    navigate('/translator/my-mangas/create');
  };

  const handleEditManga = (id: string) => {
    navigate(`/translator/my-mangas/edit/${id}`);
  };

  const handleViewManga = (id: string) => {
    // Mở manga trong tab mới để xem
    window.open(`/manga/${id}`, '_blank');
  };

  const handleDeleteManga = async (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa truyện này?')) {
      try {
        await mangaService.deleteManga(id);
        toast.success('Xóa truyện thành công', { position: 'top-right' });
        fetchMyMangas(currentPage, searchTerm);
      } catch (error) {
        console.error('Error deleting manga:', error);
        toast.error('Không thể xóa truyện', { position: 'top-right' });
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Truyện của tôi</h1>
        <Button onClick={handleCreateManga} className="bg-green-600 hover:bg-green-700">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Thêm truyện mới
        </Button>
      </div>

      <div className="mb-6">
        <SearchBar onSearch={handleSearch} placeholder="Tìm kiếm truyện..." />
      </div>

      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Đang tải...</p>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tiêu đề
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tác giả
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Trạng thái
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lượt xem
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {mangas.length > 0 ? (
                    mangas.map((manga) => (
                      <tr key={manga.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{manga.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{manga.author}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            manga.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            manga.status === 'ONGOING' ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {manga.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {manga.views?.toLocaleString() || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleViewManga(manga.id)}
                              className="text-blue-600 hover:text-blue-900 p-2 rounded-md hover:bg-blue-50"
                              title="Xem truyện"
                            >
                              <FontAwesomeIcon icon={faEye} />
                            </button>
                            <button
                              onClick={() => handleEditManga(manga.id)}
                              className="text-yellow-600 hover:text-yellow-900 p-2 rounded-md hover:bg-yellow-50"
                              title="Chỉnh sửa"
                            >
                              <FontAwesomeIcon icon={faEdit} />
                            </button>
                            <button
                              onClick={() => handleDeleteManga(manga.id)}
                              className="text-red-600 hover:text-red-900 p-2 rounded-md hover:bg-red-50"
                              title="Xóa"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-12 text-center">
                        <div className="text-gray-500">
                          <FontAwesomeIcon icon={faBook} className="text-4xl mb-4" />
                          <p className="text-lg">Bạn chưa có truyện nào</p>
                          <p className="text-sm">Hãy tạo truyện đầu tiên của bạn!</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {totalPages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default TranslatorMyMangas;
```

### Tạo trang TranslatorMyChapters.tsx (trong thư mục translator)
```tsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import chapterService from '../../services/chapter-service';
import { ChapterResponse } from '../../types/chapter';
import { Pagination } from '../../components/common/Pagination';
import { SearchBar } from '../../components/common/SearchBar';
import { Button } from '../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faEdit, faTrash, faEye, faBookOpen } from '@fortawesome/free-solid-svg-icons';

const TranslatorMyChapters = () => {
  const navigate = useNavigate();
  const [chapters, setChapters] = useState<ChapterResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch chapters created by current user
  const fetchMyChapters = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const response = await chapterService.getMyChapters(page - 1, 10, search);
      if (response) {
        setChapters(response.content);
        setTotalPages(response.totalPages);
        setCurrentPage(response.number + 1);
      }
    } catch (error) {
      console.error('Error fetching my chapters:', error);
      toast.error('Không thể tải danh sách chương của bạn', { position: 'top-right' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMyChapters(currentPage, searchTerm);
  }, [currentPage]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchMyChapters(1, term);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateChapter = () => {
    navigate('/translator/my-chapters/create');
  };

  const handleEditChapter = (id: string) => {
    navigate(`/translator/my-chapters/edit/${id}`);
  };

  const handleViewChapter = (mangaId: string, chapterNumber: number) => {
    // Mở chapter trong tab mới để xem
    window.open(`/manga/${mangaId}/chapter/${chapterNumber}`, '_blank');
  };

  const handleDeleteChapter = async (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa chương này?')) {
      try {
        await chapterService.deleteChapter(id);
        toast.success('Xóa chương thành công', { position: 'top-right' });
        fetchMyChapters(currentPage, searchTerm);
      } catch (error) {
        console.error('Error deleting chapter:', error);
        toast.error('Không thể xóa chương', { position: 'top-right' });
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Chương của tôi</h1>
        <Button onClick={handleCreateChapter} className="bg-green-600 hover:bg-green-700">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Thêm chương mới
        </Button>
      </div>

      <div className="mb-6">
        <SearchBar onSearch={handleSearch} placeholder="Tìm kiếm chương..." />
      </div>

      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Đang tải...</p>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tên chương
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Truyện
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Số chương
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lượt xem
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {chapters.length > 0 ? (
                    chapters.map((chapter) => (
                      <tr key={chapter.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{chapter.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{chapter.mangaTitle}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">Chương {chapter.chapterNumber}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {chapter.views?.toLocaleString() || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleViewChapter(chapter.mangaId, chapter.chapterNumber)}
                              className="text-blue-600 hover:text-blue-900 p-2 rounded-md hover:bg-blue-50"
                              title="Xem chương"
                            >
                              <FontAwesomeIcon icon={faEye} />
                            </button>
                            <button
                              onClick={() => handleEditChapter(chapter.id)}
                              className="text-yellow-600 hover:text-yellow-900 p-2 rounded-md hover:bg-yellow-50"
                              title="Chỉnh sửa"
                            >
                              <FontAwesomeIcon icon={faEdit} />
                            </button>
                            <button
                              onClick={() => handleDeleteChapter(chapter.id)}
                              className="text-red-600 hover:text-red-900 p-2 rounded-md hover:bg-red-50"
                              title="Xóa"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-12 text-center">
                        <div className="text-gray-500">
                          <FontAwesomeIcon icon={faBookOpen} className="text-4xl mb-4" />
                          <p className="text-lg">Bạn chưa có chương nào</p>
                          <p className="text-sm">Hãy tạo chương đầu tiên của bạn!</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {totalPages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default TranslatorMyChapters;
```

### Cập nhật MangaService.ts
```typescript
// Thêm các phương thức mới
const getMyMangas = async (page = 0, size = 10, search = '') => {
  try {
    const response = await axiosInstance.get('/mangas', {
      params: {
        page,
        size,
        keyword: search || undefined
      }
    });
    return response.data.result;
  } catch (error) {
    console.error('Error fetching my mangas:', error);
    throw error;
  }
};

// Xuất các phương thức
const mangaService = {
  // Các phương thức hiện có
  getMyMangas,
  // Các phương thức khác
};
```

### Cập nhật ChapterService.ts
```typescript
// Thêm các phương thức mới
const getMyChapters = async (page = 0, size = 10, search = '') => {
  try {
    const response = await axiosInstance.get('/chapters/my-chapters', {
      params: {
        page,
        size,
        keyword: search || undefined
      }
    });
    return response.data.result;
  } catch (error) {
    console.error('Error fetching my chapters:', error);
    throw error;
  }
};

// Xuất các phương thức
const chapterService = {
  // Các phương thức hiện có
  getMyChapters,
  // Các phương thức khác
};
```

### Cập nhật Routes.tsx
```tsx
import TranslatorMyMangas from '../pages/translator/TranslatorMyMangas';
import TranslatorMyChapters from '../pages/translator/TranslatorMyChapters';
import TranslatorLayout from '../components/layouts/TranslatorLayout';

// Thêm translator routes
{
  path: '/translator',
  Component: DefaultRedirect, // Redirect to /translator/my-mangas
  Layout: null,
  requireAuth: true
},
{
  path: '/translator/my-mangas',
  element: (
    <ProtectedRoute requiredPermission="TRANSLATOR_MANAGEMENT">
      <TranslatorLayout>
        <TranslatorMyMangas />
      </TranslatorLayout>
    </ProtectedRoute>
  )
},
{
  path: '/translator/my-chapters',
  element: (
    <ProtectedRoute requiredPermission="TRANSLATOR_MANAGEMENT">
      <TranslatorLayout>
        <TranslatorMyChapters />
      </TranslatorLayout>
    </ProtectedRoute>
  )
},
{
  path: '/translator/my-mangas/create',
  element: (
    <ProtectedRoute requiredPermission="TRANSLATOR_MANAGEMENT">
      <TranslatorLayout>
        <CreateManga />
      </TranslatorLayout>
    </ProtectedRoute>
  )
},
{
  path: '/translator/my-mangas/edit/:id',
  element: (
    <ProtectedRoute requiredPermission="TRANSLATOR_MANAGEMENT">
      <TranslatorLayout>
        <EditManga />
      </TranslatorLayout>
    </ProtectedRoute>
  )
},
{
  path: '/translator/my-chapters/create',
  element: (
    <ProtectedRoute requiredPermission="TRANSLATOR_MANAGEMENT">
      <TranslatorLayout>
        <CreateChapter />
      </TranslatorLayout>
    </ProtectedRoute>
  )
},
{
  path: '/translator/my-chapters/edit/:id',
  element: (
    <ProtectedRoute requiredPermission="TRANSLATOR_MANAGEMENT">
      <TranslatorLayout>
        <EditChapter />
      </TranslatorLayout>
    </ProtectedRoute>
  )
},
```

### Cập nhật DefaultRedirect.tsx
```tsx
import { useAuth } from '../contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const DefaultRedirect = () => {
  const { hasMangaManagement, hasSystemManagement, hasTranslatorManagement } = useAuth();

  if (hasSystemManagement || hasMangaManagement) {
    return <Navigate to="/admin/dashboard" replace />;
  } else if (hasTranslatorManagement) {
    return <Navigate to="/translator/my-mangas" replace />;
  } else {
    return <Navigate to="/login" replace />;
  }
};

export default DefaultRedirect;
```

### Tạo ProtectedRoute component
```tsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredPermission }) => {
  const { isAuthenticated, permissions } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (!permissions.includes(requiredPermission)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
```

## 4. CẬP NHẬT API GATEWAY

Không cần thay đổi gì ở API Gateway vì nó chỉ chuyển tiếp các yêu cầu.

## 5. CẬP NHẬT UPLOAD SERVICE

### Cập nhật SecurityConfig.java
```java
@Bean
public SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception {
    httpSecurity.authorizeHttpRequests(request ->
            request.requestMatchers(HttpMethod.GET, PUBLIC_ENDPOINTS).permitAll()
                    .requestMatchers(HttpMethod.POST, "/manga", "/avatar").hasAnyAuthority("MANGA_MANAGEMENT", "SYSTEM_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.DELETE, "/{fileName}").hasAnyAuthority("MANGA_MANAGEMENT", "SYSTEM_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .anyRequest()
                    .authenticated());

    // Phần còn lại giữ nguyên
}
```

## 6. CẬP NHẬT BACKEND API

### Thêm endpoint getMyChapters trong ChapterController.java
```java
@GetMapping("/my-chapters")
@PreAuthorize("hasAuthority('TRANSLATOR_MANAGEMENT')")
ApiResponse<Page<ChapterResponse>> getMyChapters(
        @RequestParam(value = "keyword", required = false) String keyword,
        @PageableDefault(size = 10) Pageable pageable,
        @AuthenticationPrincipal Jwt jwt
) {
    String userId = jwt.getSubject();
    return ApiResponse.<Page<ChapterResponse>>builder()
            .message("My chapters retrieved successfully")
            .result(chapterService.getChaptersByCreatedBy(userId, keyword, pageable))
            .build();
}
```

### Thêm method getChaptersByCreatedBy trong ChapterService.java
```java
public Page<ChapterResponse> getChaptersByCreatedBy(String createdBy, String keyword, Pageable pageable) {
    Page<Chapter> chapters;

    if (keyword != null && !keyword.trim().isEmpty()) {
        chapters = chapterRepository.findByCreatedByAndTitleContainingIgnoreCase(createdBy, keyword, pageable);
    } else {
        chapters = chapterRepository.findByCreatedBy(createdBy, pageable);
    }

    return chapters.map(this::buildChapterResponse);
}
```

### Cập nhật ChapterRepository.java
```java
Page<Chapter> findByCreatedBy(String createdBy, Pageable pageable);

Page<Chapter> findByCreatedByAndTitleContainingIgnoreCase(String createdBy, String keyword, Pageable pageable);
```

## 7. CẤU TRÚC THỦ MỤC TRANSLATOR PORTAL

```
src/
├── components/
│   ├── layouts/
│   │   ├── AdminLayout.tsx (existing)
│   │   └── TranslatorLayout.tsx (new)
│   └── guards/
│       └── ProtectedRoute.tsx (new)
├── pages/
│   ├── admin/ (existing)
│   └── translator/ (new)
│       ├── TranslatorMyMangas.tsx
│       ├── TranslatorMyChapters.tsx
│       ├── CreateManga.tsx (reuse from admin)
│       ├── EditManga.tsx (reuse from admin)
│       ├── CreateChapter.tsx (reuse from admin)
│       └── EditChapter.tsx (reuse from admin)
└── routes/
    └── index.ts (updated)
```

## 8. HƯỚNG DẪN TRIỂN KHAI

### Bước 1: Database Migration
```sql
-- Thêm cột created_by vào bảng manga và chapter
ALTER TABLE manga ADD COLUMN created_by VARCHAR(255);
ALTER TABLE chapter ADD COLUMN created_by VARCHAR(255);

-- Cập nhật dữ liệu hiện có (gán cho admin)
UPDATE manga SET created_by = 'admin-user-id' WHERE created_by IS NULL;
UPDATE chapter SET created_by = 'admin-user-id' WHERE created_by IS NULL;

-- Thêm permission và role mới sẽ được tự động tạo qua ApplicationInitConfig
```

### Bước 2: Triển khai Backend (theo thứ tự)
1. **Identity Service**: Cập nhật ApplicationInitConfig.java
2. **Manga Service**:
   - Cập nhật Entity (Manga.java, Chapter.java)
   - Cập nhật Repository (MangaRepository.java, ChapterRepository.java)
   - Cập nhật Service (MangaService.java, ChapterService.java)
   - Cập nhật Controller (MangaController.java, ChapterController.java)
   - Cập nhật SecurityConfig.java
3. **Upload Service**: Cập nhật SecurityConfig.java

### Bước 3: Triển khai Frontend
1. **Tạo TranslatorLayout.tsx**
2. **Tạo translator pages** (TranslatorMyMangas.tsx, TranslatorMyChapters.tsx)
3. **Cập nhật AuthContext.tsx** với logic redirect
4. **Cập nhật Routes.tsx** với translator routes
5. **Tạo ProtectedRoute component**
6. **Cập nhật DefaultRedirect.tsx**

### Bước 4: Kiểm tra (tôi sẽ tự kiểm tra)
1. **Admin functions**:
   - Đăng nhập với tài khoản admin
   - Gán role TRANSLATOR cho một số người dùng
   - Kiểm tra admin vẫn có thể xem/sửa/xóa tất cả truyện

2. **Translator functions**:
   - Đăng nhập với tài khoản có role TRANSLATOR
   - Kiểm tra redirect đến `/translator/my-mangas`
   - Kiểm tra chỉ xem được truyện/chương do mình tạo
   - Kiểm tra chỉ sửa/xóa được truyện/chương do mình tạo
   - Kiểm tra không thể truy cập `/admin/*` routes

3. **Security testing**:
   - Thử truy cập trực tiếp `/admin/*` với tài khoản translator
   - Thử sửa/xóa truyện của người khác
   - Kiểm tra API endpoints có đúng authorization không
