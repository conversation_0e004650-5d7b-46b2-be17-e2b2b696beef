name: raindrop
services:
  kafka:
    image: 'bitnami/kafka:3.7.0'
    container_name: kafka
    hostname: kafka
    ports:
      - '9094:9094'
    environment:
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094
      - <PERSON>AF<PERSON>_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,EXTERNAL://localhost:9094
      - K<PERSON>KA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
    networks:
      - raindrop_network

  redis:
    image: redis:7.0
    container_name: redis
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - raindrop_network
    command: [ "redis-server", "--appendonly", "yes" ]  # Enable AOF persistence for Redis

volumes:
  redis_data:
    driver: local

networks:
  raindrop_network:
    driver: bridge