server:
  port: 8084
  servlet:
    context-path: /notification

spring:
  datasource:
    url: "************************************************"
    username: "root"
    password: ""
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  kafka:
    bootstrap-servers: localhost:9094
    consumer:
      group-id: notification-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"

app:
  email:
    api-key: "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"
