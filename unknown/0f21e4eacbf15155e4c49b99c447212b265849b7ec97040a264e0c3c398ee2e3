server:
  port: 8085
  servlet:
    context-path: /comment

spring:
  application:
    name: comment-service
  datasource:
    url: *******************************************
    username: root
    password:
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  kafka:
    bootstrap-servers: localhost:9094
    consumer:
      group-id: comment-service
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: com.raindrop.common.event
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

logging:
  level:
    com.raindrop.comment_service: DEBUG
    org.springframework.security: DEBUG

# Security configuration
security:
  oauth2:
    resource-server:
      jwt:
        issuer-uri: http://localhost:8080/identity-service
        jwk-set-uri: http://localhost:8080/identity-service/.well-known/jwks.json

# API URLs
app:
  services:
    manga: http://localhost:8082/manga
    identity: http://localhost:8080/identity

# Feign Client Configuration
feign:
  httpclient:
    enabled: true
    max-connections: 200
    max-connections-per-route: 50
    disable-ssl-validation: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      profile-service:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
        decode404: true
