package com.raindrop.manga_service.mapper;

import com.raindrop.manga_service.dto.response.ChapterResponse;
import com.raindrop.manga_service.dto.response.PageResponse;
import com.raindrop.manga_service.entity.Chapter;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.entity.Page;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-05-31T13:42:32+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Oracle Corporation)"
)
@Component
public class ChapterMapperImpl implements ChapterMapper {

    @Override
    public ChapterResponse toChapterResponse(Chapter chapter) {
        if ( chapter == null ) {
            return null;
        }

        ChapterResponse.ChapterResponseBuilder chapterResponse = ChapterResponse.builder();

        chapterResponse.id( chapter.getId() );
        chapterResponse.views( chapter.getViews() );
        chapterResponse.comments( chapter.getComments() );
        chapterResponse.mangaId( chapterMangaId( chapter ) );
        chapterResponse.chapterNumber( chapter.getChapterNumber() );
        chapterResponse.title( chapter.getTitle() );
        chapterResponse.pages( pageListToPageResponseList( chapter.getPages() ) );
        chapterResponse.updatedAt( chapter.getUpdatedAt() );

        return chapterResponse.build();
    }

    private String chapterMangaId(Chapter chapter) {
        Manga manga = chapter.getManga();
        if ( manga == null ) {
            return null;
        }
        return manga.getId();
    }

    protected PageResponse pageToPageResponse(Page page) {
        if ( page == null ) {
            return null;
        }

        PageResponse.PageResponseBuilder pageResponse = PageResponse.builder();

        pageResponse.index( page.getIndex() );
        pageResponse.pageUrl( page.getPageUrl() );

        return pageResponse.build();
    }

    protected List<PageResponse> pageListToPageResponseList(List<Page> list) {
        if ( list == null ) {
            return null;
        }

        List<PageResponse> list1 = new ArrayList<PageResponse>( list.size() );
        for ( Page page : list ) {
            list1.add( pageToPageResponse( page ) );
        }

        return list1;
    }
}
