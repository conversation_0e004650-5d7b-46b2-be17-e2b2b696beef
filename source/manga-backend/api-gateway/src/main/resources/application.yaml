server:
  port: 8888
app:
  api-prefix: /api/v1
spring:
  application:
    name: api-gateway
  codec:
    max-in-memory-size: 10MB
  cloud:
    gateway:
      routes:
        - id: identity_service
          uri: http://localhost:8080
          predicates:
            - Path=${app.api-prefix}/identity/**
          filters:
            - StripPrefix=2
        - id: profile_service
          uri: http://localhost:8081
          predicates:
            - Path=${app.api-prefix}/profile/**
          filters:
            - StripPrefix=2
        - id: manga_service
          uri: http://localhost:8082
          predicates:
            - Path=${app.api-prefix}/manga/**
          filters:
            - StripPrefix=2
        - id: upload_service
          uri: http://localhost:8083
          predicates:
            - Path=${app.api-prefix}/upload/**
          filters:
            - StripPrefix=2
        - id: history_service
          uri: http://localhost:8087
          predicates:
            - Path=${app.api-prefix}/history/**
          filters:
            - StripPrefix=2
        - id: comment_service
          uri: http://localhost:8085
          predicates:
            - Path=${app.api-prefix}/comment/**
          filters:
            - StripPrefix=2
        - id: favorite_service
          uri: http://localhost:8086
          predicates:
            - Path=${app.api-prefix}/favorite/**
          filters:
            - StripPrefix=2
