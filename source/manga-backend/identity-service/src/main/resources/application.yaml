server:
  port: 8080
  servlet:
    context-path: /identity

google:
  client-id: 490201931226-f3motu1vf7bbe832sa0q33719buee5ou.apps.googleusercontent.com
  client-secret: 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
  redirect-uri: http://localhost:5173/authenticate
  token-uri: https://oauth2.googleapis.com/token
  user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo

spring:
  datasource:
    url: "********************************************"
    username: "root"
    password: ""
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  data:
    web:
      pageable:
        default-page-size: 10
        max-page-size: 100
        page-parameter: page
        size-parameter: size
        one-indexed-parameters: false
    redis:
      host: localhost
      port: 6379
      timeout: 10000
      database: 0
  kafka:
    bootstrap-servers: localhost:9094
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
jwt:
  signerKey: "o6lSCt2tIkiqLnuj/m+P/My5Nq4w6C47rvMCAQIXJp8+I4lxliuh/EMEFM/YS9Aa"
app:
  services:
    upload: http://localhost:8083/upload