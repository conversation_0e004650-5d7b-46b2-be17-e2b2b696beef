server:
  port: 8083
  servlet:
    context-path: /upload
    multipart:
      max-file-size: 50MB
      max-request-size: 1000MB
  tomcat:
    max-http-form-post-size: 1000MB
    max-swallow-size: 1000MB

spring:
  datasource:
    url: "******************************************"
    username: "root"
    password: ""
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  web:
    resources:
      static-locations: file:uploads/,classpath:/static
app:
  upload:
    url: http://localhost:8083/upload/files/
    manga: C:/uploads/manga/
    user: C:/uploads/user/

jwt:
  signerKey: "o6lSCt2tIkiqLnuj/m+P/My5Nq4w6C47rvMCAQIXJp8+I4lxliuh/EMEFM/YS9Aa"