# React + JavaScript + Vite

This project provides a React application setup with Vite and JavaScript, featuring HMR and ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Project Migration

This project has been migrated from TypeScript to JavaScript for simplified development and reduced complexity.

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Start development server:
```bash
npm run dev
```

3. Build for production:
```bash
npm run build
```

## Features

- React 19 with modern hooks
- Vite for fast development and building
- ESLint for code quality
- TailwindCSS for styling
- FontAwesome icons
- React Router for navigation
- Axios for HTTP requests
- React Toastify for notifications
