import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUndo, faEye, faSearch, faTrash } from '@fortawesome/free-solid-svg-icons';
import mangaService from '../../services/manga-service';
import Pagination from '../../components/common/Pagination';
import { MangaManagementResponse } from '../../types/manga';

const TranslatorDeletedMangas: React.FC = () => {
  const [deletedMangas, setDeletedMangas] = useState<MangaManagementResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Fetch danh sách truyện đã xóa
  const fetchDeletedMangas = async (page: number = 1, size: number = 10) => {
    setLoading(true);
    try {
      const response = await mangaService.getMyDeletedMangas(page - 1, size);
      if (response) {
        setDeletedMangas(response.content);
        setTotalPages(response.totalPages);
        setTotalItems(response.totalElements);
        setCurrentPage(page);
        setPageSize(size);
      }
    } catch (error) {
      console.error('Error fetching deleted mangas:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeletedMangas();
  }, []);

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    fetchDeletedMangas(page, pageSize);
  };

  // Xử lý thay đổi kích thước trang
  const handlePageSizeChange = (size: number) => {
    fetchDeletedMangas(1, size);
  };

  // Xử lý khôi phục manga
  const handleRestoreManga = async (mangaId: string) => {
    if (window.confirm('Bạn có chắc chắn muốn khôi phục manga này?')) {
      try {
        const success = await mangaService.restoreMyManga(mangaId);
        if (success) {
          // Refresh danh sách
          await fetchDeletedMangas(currentPage, pageSize);
        }
      } catch (error) {
        console.error('Error restoring manga:', error);
      }
    }
  };

  // Xử lý xem manga
  const handleViewManga = (mangaId: string) => {
    // Mở manga trong tab mới để xem
    window.open(`/manga/${mangaId}`, '_blank');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          <FontAwesomeIcon icon={faTrash} className="mr-3 text-red-600" />
          Truyện Đã Xóa
        </h1>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Ảnh bìa
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Tiêu đề
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Tác giả
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Số chapter
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Ngày xóa
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {deletedMangas.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      Không có truyện nào đã bị xóa
                    </td>
                  </tr>
                ) : (
                  deletedMangas.map((manga) => (
                    <tr key={manga.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <img
                          src={manga.coverUrl || '/default-cover.jpg'}
                          alt={manga.title}
                          className="h-16 w-12 object-cover rounded"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {manga.title}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {manga.author}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {manga.chapters}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                          Đã xóa
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {manga.deletedAt ? new Date(manga.deletedAt).toLocaleDateString('vi-VN') : 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          onClick={() => handleViewManga(manga.id)}
                          title="Xem"
                        >
                          <FontAwesomeIcon icon={faEye} />
                        </button>
                        <button
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          onClick={() => handleRestoreManga(manga.id)}
                          title="Khôi phục"
                        >
                          <FontAwesomeIcon icon={faUndo} />
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Phân trang */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        totalItems={totalItems}
        showingFrom={(currentPage - 1) * pageSize + 1}
        showingTo={Math.min(currentPage * pageSize, totalItems)}
        pageSize={pageSize}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};

export default TranslatorDeletedMangas;
